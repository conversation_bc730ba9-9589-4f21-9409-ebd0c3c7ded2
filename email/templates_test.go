package email

import (
	"strings"
	"testing"
	"time"
)

func TestGetPasswordResetSuccessEmail(t *testing.T) {
	username := "张三"
	loginLink := "https://example.com/login"

	tests := []struct {
		name     string
		language string
		wantErr  bool
	}{
		{
			name:     "Chinese password reset success email",
			language: "zh",
			wantErr:  false,
		},
		{
			name:     "English password reset success email", 
			language: "en",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			htmlContent, textContent, err := GetPasswordResetSuccessEmail(username, loginLink, tt.language)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPasswordResetSuccessEmail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// 验证HTML内容包含必要的元素
				if !strings.Contains(htmlContent, username) {
					t.Errorf("HTML content should contain username %s", username)
				}
				if !strings.Contains(htmlContent, loginLink) {
					t.<PERSON>("HTML content should contain login link %s", loginLink)
				}

				// 验证文本内容包含必要的元素
				if !strings.Contains(textContent, username) {
					t.Errorf("Text content should contain username %s", username)
				}
				if !strings.Contains(textContent, loginLink) {
					t.Errorf("Text content should contain login link %s", loginLink)
				}

				// 验证语言特定的内容
				if tt.language == "zh" {
					if !strings.Contains(htmlContent, "密码重置成功") {
						t.Errorf("Chinese HTML content should contain '密码重置成功'")
					}
					if !strings.Contains(textContent, "密码重置成功") {
						t.Errorf("Chinese text content should contain '密码重置成功'")
					}
					if !strings.Contains(htmlContent, "立即登录") {
						t.Errorf("Chinese HTML content should contain '立即登录'")
					}
				} else {
					if !strings.Contains(htmlContent, "Password Reset Successful") {
						t.Errorf("English HTML content should contain 'Password Reset Successful'")
					}
					if !strings.Contains(textContent, "Password Reset Successful") {
						t.Errorf("English text content should contain 'Password Reset Successful'")
					}
					if !strings.Contains(htmlContent, "Login Now") {
						t.Errorf("English HTML content should contain 'Login Now'")
					}
				}

				// 验证包含当前年份
				currentYear := time.Now().Year()
				if !strings.Contains(htmlContent, string(rune(currentYear))) && !strings.Contains(htmlContent, "2024") {
					t.Logf("HTML content: %s", htmlContent[:200]) // 只打印前200个字符用于调试
				}
			}
		})
	}
}

func TestGetPasswordResetSuccessTranslations(t *testing.T) {
	tests := []struct {
		name     string
		language string
		expected map[string]string
	}{
		{
			name:     "Chinese translations",
			language: "zh",
			expected: map[string]string{
				"title":           "密码重置成功",
				"header":          "密码重置成功",
				"greeting":        "您好",
				"success_message": "您的密码已成功重置！您现在可以使用新密码登录您的账户。",
				"login_button":    "立即登录",
			},
		},
		{
			name:     "English translations",
			language: "en",
			expected: map[string]string{
				"title":           "Password Reset Successful",
				"header":          "Password Reset Successful",
				"greeting":        "Hello",
				"success_message": "Your password has been successfully reset! You can now log in to your account using your new password.",
				"login_button":    "Login Now",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translations := getPasswordResetSuccessTranslations(tt.language)
			
			for key, expectedValue := range tt.expected {
				if actualValue, exists := translations[key]; !exists {
					t.Errorf("Translation key '%s' not found", key)
				} else if actualValue != expectedValue {
					t.Errorf("Translation for key '%s' = %v, want %v", key, actualValue, expectedValue)
				}
			}
		})
	}
}
