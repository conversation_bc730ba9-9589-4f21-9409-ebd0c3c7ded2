package email

import (
	"context"
	"crypto/tls"
	"fmt"
	"log/slog"
	"net/smtp"
	"strconv"

	"github.com/jordan-wright/email"
	"gitlab.glm.ai/ai-search/z-ai-email-server/utils"
)

// SMTPConfig 存储SMTP服务器配置
type SMTPConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	FromName string
	FromAddr string
	UseTLS   bool
}

var (
	// 全局SMTP配置
	config SMTPConfig
)

// 从环境变量加载SMTP配置
func init() {
	utils.LoadEnvFile()

	port, _ := strconv.Atoi(getEnvWithDefault("SMTP_PORT", "587"))
	useTLS, _ := strconv.ParseBool(getEnvWithDefault("SMTP_USE_TLS", "true"))

	config = SMTPConfig{
		Host:     getEnvWithDefault("SMTP_HOST", ""),
		Port:     port,
		Username: getEnvWithDefault("SMTP_USERNAME", ""),
		Password: getEnvWithDefault("SMTP_PASSWORD", ""),
		FromName: getEnvWithDefault("SMTP_FROM_NAME", "Open-WebUI"),
		FromAddr: getEnvWithDefault("SMTP_FROM_ADDR", ""),
		UseTLS:   useTLS,
	}

	slog.Info("已初始化SMTP配置",
		"host", config.Host,
		"port", config.Port,
		"username", config.Username,
		"fromName", config.FromName,
		"fromAddr", config.FromAddr,
		"useTLS", config.UseTLS)
}

// SendEmailWithTemplate 发送带模板的邮件
func SendEmailWithTemplate(ctx context.Context, to []string, subject, htmlBody, textBody string) error {
	// 从上下文中获取trace_id
	traceID := getTraceIDFromContext(ctx)
	logger := slog.With("trace_id", traceID, "module", "email")

	logger.Info("开始发送邮件", "to", to, "subject", subject)

	if config.Host == "" || config.Username == "" || config.Password == "" || config.FromAddr == "" {
		logger.Error("SMTP配置不完整")
		return fmt.Errorf("SMTP配置不完整")
	}

	e := email.NewEmail()
	e.From = fmt.Sprintf("%s <%s>", config.FromName, config.FromAddr)

	e.To = to
	e.Subject = subject
	e.HTML = []byte(htmlBody)
	e.Text = []byte(textBody)

	var err error
	addr := fmt.Sprintf("%s:%d", config.Host, config.Port)

	if config.UseTLS {
		auth := smtp.PlainAuth("", config.Username, config.Password, config.Host)
		err = e.SendWithTLS(
			addr,
			auth,
			&tls.Config{ServerName: config.Host},
		)
	} else {
		auth := smtp.PlainAuth("", config.Username, config.Password, config.Host)
		err = e.Send(addr, auth)
	}

	if err != nil {
		logger.Error("邮件发送失败", "error", err)
		return err
	}

	logger.Info("邮件发送成功", "to", to)
	return nil
}

// getTraceIDFromContext 从上下文中获取trace_id
func getTraceIDFromContext(ctx context.Context) string {
	if ctx == nil {
		return "unknown"
	}

	// 根据您实际的trace_id实现方式进行调整
	// 这里假设trace_id是作为字符串值存储在上下文中的
	if traceID, ok := ctx.Value("trace_id").(string); ok && traceID != "" {
		return traceID
	}
	return "unknown"
}
