package email

import (
	"bytes"
	"fmt"
	"html/template"
	"os"
	"time"
)

// VerificationEmailData 邮件模板数据结构 (Structure remains)
type VerificationEmailData struct {
	Username     string
	VerifyLink   string
	VerifyCode   string
	ExpireTime   string
	CompanyName  string
	CurrentYear  int
	SupportEmail string
	Language     string
	Translations map[string]string
}

// GetVerificationEmail (Function remains)
func GetVerificationEmail(username, verifyLink, verifyCode string, expireMinutes int, language string) (string, string, error) {
	companyName := getEnvWithDefault("COMPANY_NAME", "Z.ai")
	supportEmail := getEnvWithDefault("SUPPORT_EMAIL", "<EMAIL>")

	if language != "en" {
		language = "zh"
	}

	var expireTimeStr string
	if language == "zh" {
		expireTimeStr = "24小时"
	} else {
		expireTimeStr = "24 hours"
	}

	translations := getVerificationTranslations(language) // Use the original function here

	data := VerificationEmailData{
		Username:     username,
		VerifyLink:   verifyLink,
		VerifyCode:   verifyCode,
		ExpireTime:   expireTimeStr,
		CompanyName:  companyName,
		CurrentYear:  time.Now().Year(),
		SupportEmail: supportEmail,
		Language:     language,
		Translations: translations,
	}

	var htmlTemplate, textTemplate string
	if language == "zh" {
		htmlTemplate = verificationHTMLTemplateZhUpdated // Use the original updated template here
		textTemplate = verificationTextTemplateZh        // Use the original text template here
	} else {
		htmlTemplate = verificationHTMLTemplateEnUpdated // Use the original updated template here
		textTemplate = verificationTextTemplateEn        // Use the original text template here
	}

	htmlContent, err := renderTemplate(htmlTemplate, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to render HTML template: %w", err)
	}

	textContent, err := renderTemplate(textTemplate, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to render text template: %w", err)
	}

	return htmlContent, textContent, nil
}

// getVerificationTranslations (Function remains)
func getVerificationTranslations(language string) map[string]string {
	translations := make(map[string]string)
	if language == "zh" {
		translations["title"] = "验证您的电子邮箱"
		translations["header"] = "邮箱验证"
		translations["greeting"] = "您好"
		translations["thankyou"] = "感谢您注册"
		translations["verify_instruction"] = "请点击下方按钮验证您的电子邮箱地址："
		translations["verify_button"] = "验证邮箱"
		translations["expire_notice_prefix"] = "此验证链接将在"
		translations["expire_notice_suffix"] = "后过期。如需重新发送验证邮件，旧的验证链接将自动失效。"
		translations["ignore_notice_prefix"] = "如果您没有请求此验证，请忽略此邮件或联系我们的支持团队："
		translations["ignore_notice_suffix"] = "。"
		translations["rights_reserved"] = "保留所有权利。"
	} else {
		translations["title"] = "Verify Your Email"
		translations["header"] = "Email Verification"
		translations["greeting"] = "Hello"
		translations["thankyou"] = "Thank you for registering with"
		translations["verify_instruction"] = "Please click the button below to verify your email address:"
		translations["verify_button"] = "Verify Email"
		translations["expire_notice_prefix"] = "This verification link will expire in"
		translations["expire_notice_suffix"] = ". If you request a new verification email, this link will become invalid."
		translations["ignore_notice_prefix"] = "If you didn't request this verification, please ignore this email or contact our support team at"
		translations["ignore_notice_suffix"] = "."
		translations["rights_reserved"] = "All rights reserved."
	}
	return translations
}

// --- Welcome Email Code (Modified) ---

// WelcomeEmailData 欢迎邮件数据结构 (Structure remains)
type WelcomeEmailData struct {
	Username     string
	CompanyName  string
	LoginLink    string
	CurrentYear  int
	SupportEmail string
	Language     string
	Translations map[string]string
}

// GetWelcomeEmail 生成欢迎邮件（已简化）
// Generates welcome email (simplified)
func GetWelcomeEmail(username, loginLink string, language string) (string, string, error) {
	companyName := getEnvWithDefault("COMPANY_NAME", "Open-WebUI")
	supportEmail := getEnvWithDefault("SUPPORT_EMAIL", "<EMAIL>")

	if language != "en" {
		language = "zh"
	}

	// 获取已移除特性介绍的翻译文本
	translations := getWelcomeTranslationsSimplified(language) // Use the simplified translation getter

	data := WelcomeEmailData{
		Username:     username,
		CompanyName:  companyName,
		LoginLink:    loginLink,
		CurrentYear:  time.Now().Year(),
		SupportEmail: supportEmail,
		Language:     language,
		Translations: translations,
	}

	var htmlTemplate, textTemplate string
	if language == "zh" {
		htmlTemplate = welcomeHTMLTemplateZhSimplified // Use the simplified HTML template
		textTemplate = welcomeTextTemplateZhSimplified // Use the simplified text template
	} else {
		htmlTemplate = welcomeHTMLTemplateEnSimplified // Use the simplified HTML template
		textTemplate = welcomeTextTemplateEnSimplified // Use the simplified text template
	}

	htmlContent, err := renderTemplate(htmlTemplate, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to render HTML template: %w", err)
	}

	textContent, err := renderTemplate(textTemplate, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to render text template: %w", err)
	}

	return htmlContent, textContent, nil
}

// 获取欢迎邮件的翻译文本（已移除特性介绍）
// Gets translated texts for the welcome email (features section removed)
func getWelcomeTranslationsSimplified(language string) map[string]string {
	translations := make(map[string]string)

	if language == "zh" {
		translations["title"] = "欢迎加入"
		translations["header"] = "欢迎加入"
		translations["greeting"] = "尊敬的"
		translations["activated"] = "您的账户已成功激活！我们很高兴您的加入。"
		translations["login_button"] = "登录您的账户"
		translations["support_prefix"] = "如果您有任何问题或需要帮助，请随时联系我们的支持团队："
		translations["support_suffix"] = ""
		translations["rights_reserved"] = "保留所有权利。"
	} else {
		translations["title"] = "Welcome to"
		translations["header"] = "Welcome to"
		translations["greeting"] = "Dear"
		translations["activated"] = "Your account has been successfully activated! We're thrilled to have you join."
		translations["login_button"] = "Login to Your Account"
		translations["support_prefix"] = "If you have any questions or need assistance, feel free to contact our support team at"
		translations["support_suffix"] = ""
		translations["rights_reserved"] = "All rights reserved."
	}

	return translations
}

// --- Password Reset Email Code ---

// PasswordResetEmailData 密码重置邮件数据结构
type PasswordResetEmailData struct {
	Username     string
	ResetLink    string
	ExpireTime   string
	CompanyName  string
	CurrentYear  int
	SupportEmail string
	Language     string
	Translations map[string]string
}

// PasswordResetSuccessEmailData 密码重置成功邮件数据结构
type PasswordResetSuccessEmailData struct {
	Username     string
	LoginLink    string
	CompanyName  string
	CurrentYear  int
	SupportEmail string
	Language     string
	Translations map[string]string
}

// GetPasswordResetEmail 生成密码重置邮件
func GetPasswordResetEmail(username, resetLink string, expireMinutes int, language string) (string, string, error) {
	companyName := getEnvWithDefault("COMPANY_NAME", "Z.ai")
	supportEmail := getEnvWithDefault("SUPPORT_EMAIL", "<EMAIL>")

	if language != "en" {
		language = "zh"
	}

	var expireTimeStr string
	if language == "zh" {
		expireTimeStr = "30分钟"
	} else {
		expireTimeStr = "30 minutes"
	}

	translations := getPasswordResetTranslations(language)

	data := PasswordResetEmailData{
		Username:     username,
		ResetLink:    resetLink,
		ExpireTime:   expireTimeStr,
		CompanyName:  companyName,
		CurrentYear:  time.Now().Year(),
		SupportEmail: supportEmail,
		Language:     language,
		Translations: translations,
	}

	var htmlTemplate, textTemplate string
	if language == "zh" {
		htmlTemplate = passwordResetHTMLTemplateZh
		textTemplate = passwordResetTextTemplateZh
	} else {
		htmlTemplate = passwordResetHTMLTemplateEn
		textTemplate = passwordResetTextTemplateEn
	}

	htmlContent, err := renderTemplate(htmlTemplate, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to render HTML template: %w", err)
	}

	textContent, err := renderTemplate(textTemplate, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to render text template: %w", err)
	}

	return htmlContent, textContent, nil
}

// GetPasswordResetSuccessEmail 生成密码重置成功邮件
func GetPasswordResetSuccessEmail(username, loginLink string, language string) (string, string, error) {
	companyName := getEnvWithDefault("COMPANY_NAME", "Z.ai")
	supportEmail := getEnvWithDefault("SUPPORT_EMAIL", "<EMAIL>")

	if language != "en" {
		language = "zh"
	}

	translations := getPasswordResetSuccessTranslations(language)

	data := PasswordResetSuccessEmailData{
		Username:     username,
		LoginLink:    loginLink,
		CompanyName:  companyName,
		CurrentYear:  time.Now().Year(),
		SupportEmail: supportEmail,
		Language:     language,
		Translations: translations,
	}

	var htmlTemplate, textTemplate string
	if language == "zh" {
		htmlTemplate = passwordResetSuccessHTMLTemplateZh
		textTemplate = passwordResetSuccessTextTemplateZh
	} else {
		htmlTemplate = passwordResetSuccessHTMLTemplateEn
		textTemplate = passwordResetSuccessTextTemplateEn
	}

	htmlContent, err := renderTemplate(htmlTemplate, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to render HTML template: %w", err)
	}

	textContent, err := renderTemplate(textTemplate, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to render text template: %w", err)
	}

	return htmlContent, textContent, nil
}

// getPasswordResetTranslations 获取密码重置邮件的翻译文本
func getPasswordResetTranslations(language string) map[string]string {
	translations := make(map[string]string)
	if language == "zh" {
		translations["title"] = "重置您的密码"
		translations["header"] = "密码重置"
		translations["greeting"] = "您好"
		translations["reset_instruction"] = "我们收到了您的密码重置请求。请点击下方按钮重置您的密码："
		translations["reset_button"] = "重置密码"
		translations["expire_notice_prefix"] = "此重置链接将在"
		translations["expire_notice_suffix"] = "后过期。如果您重新申请密码重置，旧的重置链接将自动失效。"
		translations["ignore_notice_prefix"] = "如果您没有请求此密码重置，请忽略此邮件或联系我们的支持团队："
		translations["ignore_notice_suffix"] = "。"
		translations["rights_reserved"] = "保留所有权利。"
	} else {
		translations["title"] = "Reset Your Password"
		translations["header"] = "Password Reset"
		translations["greeting"] = "Hello"
		translations["reset_instruction"] = "We received a request to reset your password. Please click the button below to reset your password:"
		translations["reset_button"] = "Reset Password"
		translations["expire_notice_prefix"] = "This reset link will expire in"
		translations["expire_notice_suffix"] = ". If you request a new password reset, this link will become invalid."
		translations["ignore_notice_prefix"] = "If you didn't request this password reset, please ignore this email or contact our support team at"
		translations["ignore_notice_suffix"] = "."
		translations["rights_reserved"] = "All rights reserved."
	}
	return translations
}

// getPasswordResetSuccessTranslations 获取密码重置成功邮件的翻译文本
func getPasswordResetSuccessTranslations(language string) map[string]string {
	translations := make(map[string]string)
	if language == "zh" {
		translations["title"] = "密码重置成功"
		translations["header"] = "密码重置成功"
		translations["greeting"] = "您好"
		translations["success_message"] = "您的密码已成功重置！您现在可以使用新密码登录您的账户。"
		translations["login_button"] = "立即登录"
		translations["security_notice"] = "为了您的账户安全，如果您没有进行此操作，请立即联系我们的支持团队。"
		translations["support_prefix"] = "如果您有任何问题或需要帮助，请随时联系我们的支持团队："
		translations["support_suffix"] = "。"
		translations["rights_reserved"] = "保留所有权利。"
	} else {
		translations["title"] = "Password Reset Successful"
		translations["header"] = "Password Reset Successful"
		translations["greeting"] = "Hello"
		translations["success_message"] = "Your password has been successfully reset! You can now log in to your account using your new password."
		translations["login_button"] = "Login Now"
		translations["security_notice"] = "For your account security, if you did not perform this action, please contact our support team immediately."
		translations["support_prefix"] = "If you have any questions or need assistance, feel free to contact our support team at"
		translations["support_suffix"] = "."
		translations["rights_reserved"] = "All rights reserved."
	}
	return translations
}

// --- Helper Functions (Unchanged) ---
// renderTemplate and getEnvWithDefault remain the same
func renderTemplate(tmplStr string, data interface{}) (string, error) {
	tmpl, err := template.New("email").Parse(tmplStr)
	if err != nil {
		return "", fmt.Errorf("error parsing template: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("error executing template: %w", err)
	}

	return buf.String(), nil
}

func getEnvWithDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// --- HTML/Text Templates (Verification Email Unchanged, Welcome Email Simplified) ---

// Verification HTML Templates (Use the previous updated versions)
const verificationHTMLTemplateZhUpdated = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{.Translations.title}}</title>
    </head>
<body style="margin: 0; padding: 0; width: 100% !important; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #f4f4f5;">
    <div role="article" aria-label="{{.Translations.title}}" lang="zh-CN" style="background-color: #f4f4f5; padding: 20px 0;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
            <tr>
                <td align="center">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation" style="max-width: 465px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e4e7eb; border-radius: 8px;">
                        <tr>
                            <td style="padding: 24px; text-align: center; border-bottom: 1px solid #e4e7eb;">
                                <h1 style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 24px; font-weight: 600; color: #18181b; margin: 0 0 8px;">{{.CompanyName}}</h1>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 18px; font-weight: 500; color: #18181b; margin: 0;">{{.Translations.header}}</p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 32px;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.greeting}} {{.Username}}，</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.thankyou}} {{.CompanyName}}！{{.Translations.verify_instruction}}</p>
                                <table border="0" cellspacing="0" cellpadding="0" role="presentation" style="width: 100%; margin: 32px 0;">
                                    <tr>
                                        <td align="center">
                                            <a href="{{.VerifyLink}}" target="_blank" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; display: inline-block; padding: 12px 24px; background-color: #18181b; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 14px; mso-padding-alt: 0; text-underline-color: #18181b;">
                                                <span style="mso-text-raise: 9pt;">{{.Translations.verify_button}}</span>
                                                </a>
                                        </td>
                                    </tr>
                                </table>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.expire_notice_prefix}} {{.ExpireTime}} {{.Translations.expire_notice_suffix}}</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; line-height: 1.5; color: #71717a; margin: 24px 0 0; padding-top: 24px; border-top: 1px solid #e4e7eb;">
                                    {{.Translations.ignore_notice_prefix}} <a href="mailto:{{.SupportEmail}}" style="color: #18181b; text-decoration: underline;">{{.SupportEmail}}</a>{{.Translations.ignore_notice_suffix}}
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 24px 32px; text-align: center; border-top: 1px solid #e4e7eb;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; color: #71717a; margin: 0;">&copy; {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}</p>
                            </td>
                        </tr>
                    </table>
                    </td>
            </tr>
        </table>
    </div>
</body>
</html>
`

const verificationHTMLTemplateEnUpdated = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{.Translations.title}}</title>
    </head>
<body style="margin: 0; padding: 0; width: 100% !important; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #f4f4f5;">
    <div role="article" aria-label="{{.Translations.title}}" lang="en" style="background-color: #f4f4f5; padding: 20px 0;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
            <tr>
                <td align="center">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation" style="max-width: 465px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e4e7eb; border-radius: 8px;">
                        <tr>
                            <td style="padding: 24px; text-align: center; border-bottom: 1px solid #e4e7eb;">
                                <h1 style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 24px; font-weight: 600; color: #18181b; margin: 0 0 8px;">{{.CompanyName}}</h1>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 18px; font-weight: 500; color: #18181b; margin: 0;">{{.Translations.header}}</p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 32px;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.greeting}} {{.Username}},</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.thankyou}} {{.CompanyName}}! {{.Translations.verify_instruction}}</p>
                                <table border="0" cellspacing="0" cellpadding="0" role="presentation" style="width: 100%; margin: 32px 0;">
                                    <tr>
                                        <td align="center">
                                            <a href="{{.VerifyLink}}" target="_blank" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; display: inline-block; padding: 12px 24px; background-color: #18181b; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 14px; mso-padding-alt: 0; text-underline-color: #18181b;">
                                                <span style="mso-text-raise: 9pt;">{{.Translations.verify_button}}</span>
                                                </a>
                                        </td>
                                    </tr>
                                </table>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.expire_notice_prefix}} {{.ExpireTime}}{{.Translations.expire_notice_suffix}}</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; line-height: 1.5; color: #71717a; margin: 24px 0 0; padding-top: 24px; border-top: 1px solid #e4e7eb;">
                                    {{.Translations.ignore_notice_prefix}} <a href="mailto:{{.SupportEmail}}" style="color: #18181b; text-decoration: underline;">{{.SupportEmail}}</a>{{.Translations.ignore_notice_suffix}}
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 24px 32px; text-align: center; border-top: 1px solid #e4e7eb;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; color: #71717a; margin: 0;">&copy; {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}</p>
                            </td>
                        </tr>
                    </table>
                    </td>
            </tr>
        </table>
    </div>
</body>
</html>
`

// Verification Text Templates (Use the original text templates)
const verificationTextTemplateZh = `{{.Translations.greeting}} {{.Username}}，

{{.Translations.thankyou}} {{.CompanyName}}！请复制下面的链接到浏览器地址栏来验证您的电子邮箱：

{{.VerifyLink}}

{{.Translations.expire_notice_prefix}} {{.ExpireTime}} {{.Translations.expire_notice_suffix}}

{{.Translations.ignore_notice_prefix}} {{.SupportEmail}}{{.Translations.ignore_notice_suffix}}

© {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}
`

const verificationTextTemplateEn = `{{.Translations.greeting}} {{.Username}},

{{.Translations.thankyou}} {{.CompanyName}}! Please copy the link below into your browser's address bar to verify your email:

{{.VerifyLink}}

{{.Translations.expire_notice_prefix}} {{.ExpireTime}}{{.Translations.expire_notice_suffix}}

{{.Translations.ignore_notice_prefix}} {{.SupportEmail}}{{.Translations.ignore_notice_suffix}}

© {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}
`

// Simplified 中文欢迎邮件HTML模板 (Chinese Welcome Email HTML Template)
const welcomeHTMLTemplateZhSimplified = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{.Translations.title}} {{.CompanyName}}</title>
    </head>
<body style="margin: 0; padding: 0; width: 100% !important; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #f4f4f5;">
    <div role="article" aria-label="{{.Translations.title}} {{.CompanyName}}" lang="zh-CN" style="background-color: #f4f4f5; padding: 20px 0;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
            <tr>
                <td align="center">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation" style="max-width: 465px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e4e7eb; border-radius: 8px;">
                        <tr>
                            <td style="padding: 24px; text-align: center; border-bottom: 1px solid #e4e7eb;">
                                <h1 style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 24px; font-weight: 600; color: #18181b; margin: 0;">{{.Translations.header}} {{.CompanyName}}！</h1>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 32px;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.greeting}} {{.Username}}，</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.activated}}</p>
                                <table border="0" cellspacing="0" cellpadding="0" role="presentation" style="width: 100%; margin: 32px 0;">
                                    <tr>
                                        <td align="center">
                                            <a href="{{.LoginLink}}" target="_blank" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; display: inline-block; padding: 12px 24px; background-color: #18181b; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 14px; mso-padding-alt: 0; text-underline-color: #18181b;">
                                                <span style="mso-text-raise: 9pt;">{{.Translations.login_button}}</span>
                                                </a>
                                        </td>
                                    </tr>
                                </table>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 32px 0 0;">
                                    {{.Translations.support_prefix}} <a href="mailto:{{.SupportEmail}}" style="color: #18181b; text-decoration: underline;">{{.SupportEmail}}</a>{{.Translations.support_suffix}}
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 24px 32px; text-align: center; border-top: 1px solid #e4e7eb;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; color: #71717a; margin: 0;">&copy; {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}</p>
                            </td>
                        </tr>
                    </table>
                    </td>
            </tr>
        </table>
    </div>
</body>
</html>
`

// Simplified 英文欢迎邮件HTML模板 (English Welcome Email HTML Template)
const welcomeHTMLTemplateEnSimplified = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{.Translations.title}} {{.CompanyName}}</title>
    </head>
<body style="margin: 0; padding: 0; width: 100% !important; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #f4f4f5;">
    <div role="article" aria-label="{{.Translations.title}} {{.CompanyName}}" lang="en" style="background-color: #f4f4f5; padding: 20px 0;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
            <tr>
                <td align="center">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation" style="max-width: 465px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e4e7eb; border-radius: 8px;">
                        <tr>
                            <td style="padding: 24px; text-align: center; border-bottom: 1px solid #e4e7eb;">
                                <h1 style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 24px; font-weight: 600; color: #18181b; margin: 0;">{{.Translations.header}} {{.CompanyName}}!</h1>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 32px;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.greeting}} {{.Username}},</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.activated}}</p>
                                <table border="0" cellspacing="0" cellpadding="0" role="presentation" style="width: 100%; margin: 32px 0;">
                                    <tr>
                                        <td align="center">
                                            <a href="{{.LoginLink}}" target="_blank" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; display: inline-block; padding: 12px 24px; background-color: #18181b; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 14px; mso-padding-alt: 0; text-underline-color: #18181b;">
                                                <span style="mso-text-raise: 9pt;">{{.Translations.login_button}}</span>
                                                </a>
                                        </td>
                                    </tr>
                                </table>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 32px 0 0;">
                                    {{.Translations.support_prefix}} <a href="mailto:{{.SupportEmail}}" style="color: #18181b; text-decoration: underline;">{{.SupportEmail}}</a>{{.Translations.support_suffix}}
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 24px 32px; text-align: center; border-top: 1px solid #e4e7eb;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; color: #71717a; margin: 0;">&copy; {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}</p>
                            </td>
                        </tr>
                    </table>
                    </td>
            </tr>
        </table>
    </div>
</body>
</html>
`

// Simplified 中文欢迎邮件纯文本模板 (Chinese Welcome Email Text Template)
const welcomeTextTemplateZhSimplified = `{{.Translations.greeting}} {{.Username}}，

{{.Translations.header}} {{.CompanyName}}！{{.Translations.activated}}

请通过以下链接登录您的账户：
{{.LoginLink}}

{{.Translations.support_prefix}} {{.SupportEmail}}{{.Translations.support_suffix}}

© {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}
`

// Simplified 英文欢迎邮件纯文本模板 (English Welcome Email Text Template)
const welcomeTextTemplateEnSimplified = `{{.Translations.greeting}} {{.Username}},

{{.Translations.activated}}

You can login to your account here: {{.LoginLink}}

{{.Translations.support_prefix}} {{.SupportEmail}}{{.Translations.support_suffix}}

© {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}
`

// --- Password Reset Email Templates ---

// 中文密码重置邮件HTML模板 (Chinese Password Reset Email HTML Template)
const passwordResetHTMLTemplateZh = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{.Translations.title}}</title>
    </head>
<body style="margin: 0; padding: 0; width: 100% !important; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #f4f4f5;">
    <div role="article" aria-label="{{.Translations.title}}" lang="zh-CN" style="background-color: #f4f4f5; padding: 20px 0;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
            <tr>
                <td align="center">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation" style="max-width: 465px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e4e7eb; border-radius: 8px;">
                        <tr>
                            <td style="padding: 24px; text-align: center; border-bottom: 1px solid #e4e7eb;">
                                <h1 style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 24px; font-weight: 600; color: #18181b; margin: 0 0 8px;">{{.CompanyName}}</h1>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 18px; font-weight: 500; color: #18181b; margin: 0;">{{.Translations.header}}</p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 32px;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.greeting}} {{.Username}}，</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.reset_instruction}}</p>
                                <table border="0" cellspacing="0" cellpadding="0" role="presentation" style="width: 100%; margin: 32px 0;">
                                    <tr>
                                        <td align="center">
                                            <a href="{{.ResetLink}}" target="_blank" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; display: inline-block; padding: 12px 24px; background-color: #18181b; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 14px; mso-padding-alt: 0; text-underline-color: #18181b;">
                                                <span style="mso-text-raise: 9pt;">{{.Translations.reset_button}}</span>
                                                </a>
                                        </td>
                                    </tr>
                                </table>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.expire_notice_prefix}} {{.ExpireTime}} {{.Translations.expire_notice_suffix}}</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; line-height: 1.5; color: #71717a; margin: 24px 0 0; padding-top: 24px; border-top: 1px solid #e4e7eb;">
                                    {{.Translations.ignore_notice_prefix}} <a href="mailto:{{.SupportEmail}}" style="color: #18181b; text-decoration: underline;">{{.SupportEmail}}</a>{{.Translations.ignore_notice_suffix}}
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 24px 32px; text-align: center; border-top: 1px solid #e4e7eb;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; color: #71717a; margin: 0;">&copy; {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}</p>
                            </td>
                        </tr>
                    </table>
                    </td>
            </tr>
        </table>
    </div>
</body>
</html>
`

// 英文密码重置邮件HTML模板 (English Password Reset Email HTML Template)
const passwordResetHTMLTemplateEn = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{.Translations.title}}</title>
    </head>
<body style="margin: 0; padding: 0; width: 100% !important; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #f4f4f5;">
    <div role="article" aria-label="{{.Translations.title}}" lang="en" style="background-color: #f4f4f5; padding: 20px 0;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
            <tr>
                <td align="center">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation" style="max-width: 465px; margin: 0 auto; background-color: #ffffff; border: 1px solid #e4e7eb; border-radius: 8px;">
                        <tr>
                            <td style="padding: 24px; text-align: center; border-bottom: 1px solid #e4e7eb;">
                                <h1 style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 24px; font-weight: 600; color: #18181b; margin: 0 0 8px;">{{.CompanyName}}</h1>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 18px; font-weight: 500; color: #18181b; margin: 0;">{{.Translations.header}}</p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 32px;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.greeting}} {{.Username}},</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.reset_instruction}}</p>
                                <table border="0" cellspacing="0" cellpadding="0" role="presentation" style="width: 100%; margin: 32px 0;">
                                    <tr>
                                        <td align="center">
                                            <a href="{{.ResetLink}}" target="_blank" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; display: inline-block; padding: 12px 24px; background-color: #18181b; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 14px; mso-padding-alt: 0; text-underline-color: #18181b;">
                                                <span style="mso-text-raise: 9pt;">{{.Translations.reset_button}}</span>
                                                </a>
                                        </td>
                                    </tr>
                                </table>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.expire_notice_prefix}} {{.ExpireTime}}{{.Translations.expire_notice_suffix}}</p>
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; line-height: 1.5; color: #71717a; margin: 24px 0 0; padding-top: 24px; border-top: 1px solid #e4e7eb;">
                                    {{.Translations.ignore_notice_prefix}} <a href="mailto:{{.SupportEmail}}" style="color: #18181b; text-decoration: underline;">{{.SupportEmail}}</a>{{.Translations.ignore_notice_suffix}}
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 24px 32px; text-align: center; border-top: 1px solid #e4e7eb;">
                                <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; color: #71717a; margin: 0;">&copy; {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}</p>
                            </td>
                        </tr>
                    </table>
                    </td>
            </tr>
        </table>
    </div>
</body>
</html>
`

// 中文密码重置邮件文本模板 (Chinese Password Reset Email Text Template)
const passwordResetTextTemplateZh = `{{.Translations.greeting}} {{.Username}}，

{{.Translations.reset_instruction}}

请复制下面的链接到浏览器地址栏来重置您的密码：

{{.ResetLink}}

{{.Translations.expire_notice_prefix}} {{.ExpireTime}} {{.Translations.expire_notice_suffix}}

{{.Translations.ignore_notice_prefix}} {{.SupportEmail}}{{.Translations.ignore_notice_suffix}}

© {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}
`

// 英文密码重置邮件文本模板 (English Password Reset Email Text Template)
const passwordResetTextTemplateEn = `{{.Translations.greeting}} {{.Username}},

{{.Translations.reset_instruction}}

Please copy the link below into your browser's address bar to reset your password:

{{.ResetLink}}

{{.Translations.expire_notice_prefix}} {{.ExpireTime}}{{.Translations.expire_notice_suffix}}

{{.Translations.ignore_notice_prefix}} {{.SupportEmail}}{{.Translations.ignore_notice_suffix}}

© {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}
`

// --- Password Reset Success Email Templates ---

// 中文密码重置成功邮件HTML模板 (Chinese Password Reset Success Email HTML Template)
const passwordResetSuccessHTMLTemplateZh = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{.Translations.title}}</title>
    </head>
<body style="margin: 0; padding: 0; width: 100% !important; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #f4f4f5;">
    <div role="article" aria-label="{{.Translations.title}}" lang="zh-CN" style="background-color: #f4f4f5; padding: 20px 0;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
            <tr>
                <td align="center">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation" style="max-width: 600px; margin: 0 auto;">
                        <tr>
                            <td style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); overflow: hidden;">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
                                    <tr>
                                        <td style="padding: 32px 32px 24px; text-align: center; background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                            <h1 style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 24px; font-weight: 600; color: #ffffff; margin: 0; text-align: center;">{{.Translations.header}}</h1>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 32px;">
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 16px; line-height: 1.6; color: #374151; margin: 0 0 24px;">{{.Translations.greeting}} {{.Username}}，</p>
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 16px; line-height: 1.6; color: #374151; margin: 0 0 32px;">{{.Translations.success_message}}</p>
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
                                                <tr>
                                                    <td align="center" style="padding: 0 0 32px;">
                                                        <a href="{{.LoginLink}}" target="_blank" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; display: inline-block; padding: 12px 24px; background-color: #10b981; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 14px; mso-padding-alt: 0; text-underline-color: #10b981;">
                                                            <span style="mso-text-raise: 9pt;">{{.Translations.login_button}}</span>
                                                            </a>
                                                    </td>
                                                </tr>
                                            </table>
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.security_notice}}</p>
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; line-height: 1.5; color: #71717a; margin: 24px 0 0; padding-top: 24px; border-top: 1px solid #e4e7eb;">
                                                {{.Translations.support_prefix}} <a href="mailto:{{.SupportEmail}}" style="color: #18181b; text-decoration: underline;">{{.SupportEmail}}</a>{{.Translations.support_suffix}}
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 24px 32px; text-align: center; border-top: 1px solid #e4e7eb;">
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; color: #71717a; margin: 0;">&copy; {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}</p>
                                        </td>
                                    </tr>
                                </table>
                                </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
`

// 英文密码重置成功邮件HTML模板 (English Password Reset Success Email HTML Template)
const passwordResetSuccessHTMLTemplateEn = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{.Translations.title}}</title>
    </head>
<body style="margin: 0; padding: 0; width: 100% !important; word-break: break-word; -webkit-font-smoothing: antialiased; background-color: #f4f4f5;">
    <div role="article" aria-label="{{.Translations.title}}" lang="en" style="background-color: #f4f4f5; padding: 20px 0;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
            <tr>
                <td align="center">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation" style="max-width: 600px; margin: 0 auto;">
                        <tr>
                            <td style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); overflow: hidden;">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
                                    <tr>
                                        <td style="padding: 32px 32px 24px; text-align: center; background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                                            <h1 style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 24px; font-weight: 600; color: #ffffff; margin: 0; text-align: center;">{{.Translations.header}}</h1>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 32px;">
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 16px; line-height: 1.6; color: #374151; margin: 0 0 24px;">{{.Translations.greeting}} {{.Username}},</p>
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 16px; line-height: 1.6; color: #374151; margin: 0 0 32px;">{{.Translations.success_message}}</p>
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
                                                <tr>
                                                    <td align="center" style="padding: 0 0 32px;">
                                                        <a href="{{.LoginLink}}" target="_blank" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; display: inline-block; padding: 12px 24px; background-color: #10b981; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 14px; mso-padding-alt: 0; text-underline-color: #10b981;">
                                                            <span style="mso-text-raise: 9pt;">{{.Translations.login_button}}</span>
                                                            </a>
                                                    </td>
                                                </tr>
                                            </table>
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; line-height: 1.7; color: #3f3f46; margin: 0 0 16px;">{{.Translations.security_notice}}</p>
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; line-height: 1.5; color: #71717a; margin: 24px 0 0; padding-top: 24px; border-top: 1px solid #e4e7eb;">
                                                {{.Translations.support_prefix}} <a href="mailto:{{.SupportEmail}}" style="color: #18181b; text-decoration: underline;">{{.SupportEmail}}</a>{{.Translations.support_suffix}}
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 24px 32px; text-align: center; border-top: 1px solid #e4e7eb;">
                                            <p style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 12px; color: #71717a; margin: 0;">&copy; {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}</p>
                                        </td>
                                    </tr>
                                </table>
                                </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
`

// 中文密码重置成功邮件文本模板 (Chinese Password Reset Success Email Text Template)
const passwordResetSuccessTextTemplateZh = `{{.Translations.header}}

{{.Translations.greeting}} {{.Username}}，

{{.Translations.success_message}}

请通过以下链接登录您的账户：

{{.LoginLink}}

{{.Translations.security_notice}}

{{.Translations.support_prefix}} {{.SupportEmail}}{{.Translations.support_suffix}}

© {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}
`

// 英文密码重置成功邮件文本模板 (English Password Reset Success Email Text Template)
const passwordResetSuccessTextTemplateEn = `{{.Translations.header}}

{{.Translations.greeting}} {{.Username}},

{{.Translations.success_message}}

You can login to your account here: {{.LoginLink}}

{{.Translations.security_notice}}

{{.Translations.support_prefix}} {{.SupportEmail}}{{.Translations.support_suffix}}

© {{.CurrentYear}} {{.CompanyName}}. {{.Translations.rights_reserved}}
`
