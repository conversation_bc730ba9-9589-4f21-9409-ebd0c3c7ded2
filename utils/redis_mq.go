package utils

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

var RedisURL = "redis://localhost:6379/0" // Replace with your Redis URL
var StreamPrefix = "zai:stream:"

const (
	// --- Configuration ---
	TargetTopic       = "email_verify"       // The topic to consume from
	ConsumerGroupName = "email_verify_group" // Name for the consumer group

	// Example Sentinel URLs: "redis://user:password@sentinel_host1:26379,sentinel_host2:26379/mymaster?db=0"
	// redisSentinels     = []string{"localhost:26379", "localhost:26380"} // Uncomment and configure if using Sentinels
	// redisMasterName    = "mymaster"                                    // Uncomment and configure if using Sentinels
	// redisUsername      = ""                                              // Set if needed
	// redisPassword      = ""                                              // Set if needed

	// --- Consumer Behaviour ---
	readBlockTime        = 5 * time.Second  // How long to block waiting for new messages
	readCount            = 10               // Max messages to read per XREADGROUP call
	processingTimeout    = 30 * time.Second // Max time allowed to process a message
	pendingCheckInterval = 1 * time.Minute  // How often to check for old pending messages
	minIdleTimeForClaim  = 5 * time.Minute  // Min time a message must be pending before claiming
	claimBatchSize       = 10               // Max messages to claim per check
)

func getEnvWithDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}
func init() {
	LoadEnvFile()
	RedisURL = getEnvWithDefault("REDIS_URL", "redis://localhost:6379/0")
	log.Printf("Redis URL: %s", RedisURL)
	StreamPrefix = getEnvWithDefault("STREAM_PREFIX", "zai:stream:")
	log.Printf("Stream Prefix: %s", StreamPrefix)
}

// EmailVerifyMessage defines the structure expected in the 'data' field
type EmailVerifyMessage struct {
	Event          string `json:"event"`
	Email          string `json:"email"`
	Username       string `json:"username"`
	Token          string `json:"token"`
	VerifyLink     string `json:"verify_link"`
	ResetLink      string `json:"reset_link"` // 添加密码重置链接字段
	Timestamp      int64  `json:"timestamp"`
	SenderInstance string `json:"_sender_instance"` // Capture metadata from sender
	RedisTimestamp int64  `json:"_timestamp"`       // Capture metadata from sender
	Language       string `json:"language"`
}

// MessageProcessor defines the function signature for processing messages
type MessageProcessor func(ctx context.Context, msgID string, message *EmailVerifyMessage) error

// Consumer holds the state for the Redis Stream Consumer
type Consumer struct {
	redisClient        redis.UniversalClient
	streamName         string
	groupName          string
	consumerName       string // Unique name for this instance
	processor          MessageProcessor
	ctx                context.Context
	cancel             context.CancelFunc
	wg                 sync.WaitGroup
	pendingCheckTicker *time.Ticker
	stopPendingCheck   chan struct{}
}

// NewConsumer creates and initializes a new Consumer instance
func NewConsumer(ctx context.Context, client redis.UniversalClient, topic string, groupName string, processor MessageProcessor) (*Consumer, error) {
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}
	// Generate a unique consumer name for this instance
	consumerName := fmt.Sprintf("%s-%s", hostname, uuid.NewString())
	streamName := StreamPrefix + topic

	consumerCtx, cancel := context.WithCancel(ctx)

	c := &Consumer{
		redisClient:      client,
		streamName:       streamName,
		groupName:        groupName,
		consumerName:     consumerName,
		processor:        processor,
		ctx:              consumerCtx,
		cancel:           cancel,
		stopPendingCheck: make(chan struct{}),
	}

	log.Printf("Initializing Consumer: Name=%s, Stream=%s, Group=%s", c.consumerName, c.streamName, c.groupName)

	// Attempt to create the consumer group.
	// MKSTREAM creates the stream if it doesn't exist.
	// '$' means start reading from the end of the stream for new consumers in the group.
	// Ignore BUSYGROUP error if the group already exists.
	err = client.XGroupCreateMkStream(ctx, c.streamName, c.groupName, "$").Err()
	if err != nil && !strings.Contains(err.Error(), "BUSYGROUP") {
		cancel() // Clean up context if setup fails
		return nil, fmt.Errorf("failed to create consumer group '%s' for stream '%s': %w", groupName, streamName, err)
	} else if err == nil {
		log.Printf("Created consumer group '%s' for stream '%s'", c.groupName, c.streamName)
	} else {
		log.Printf("Consumer group '%s' already exists for stream '%s'", c.groupName, c.streamName)
	}

	return c, nil
}

// Start begins the message consumption loops
func (c *Consumer) Start() {
	c.wg.Add(2) // One for main consumption, one for pending check

	// Goroutine for reading new messages
	go c.consumeMessagesLoop()

	// Goroutine for handling potentially stuck messages
	c.pendingCheckTicker = time.NewTicker(pendingCheckInterval)
	go c.handlePendingMessagesLoop()

	log.Printf("Consumer '%s' started", c.consumerName)
}

// Stop gracefully shuts down the consumer
func (c *Consumer) Stop() {
	log.Printf("Stopping consumer '%s'...", c.consumerName)
	c.cancel() // Signal goroutines to stop

	if c.pendingCheckTicker != nil {
		c.pendingCheckTicker.Stop()
	}
	close(c.stopPendingCheck) // Signal pending check loop to exit immediately if waiting

	c.wg.Wait() // Wait for goroutines to finish
	err := c.redisClient.Close()
	if err != nil {
		log.Printf("Error closing Redis client: %v", err)
	}
	log.Printf("Consumer '%s' stopped", c.consumerName)
}

// consumeMessagesLoop is the main loop for reading new messages
func (c *Consumer) consumeMessagesLoop() {
	defer c.wg.Done()
	log.Printf("[%s] Starting main consumption loop", c.consumerName)

	for {
		select {
		case <-c.ctx.Done(): // Check for shutdown signal
			log.Printf("[%s] Stopping main consumption loop due to context cancellation", c.consumerName)
			return
		default:
			// Read messages assigned to this consumer in the group
			// '>' means read only new messages (not delivered to any consumer yet)
			streams, err := c.redisClient.XReadGroup(c.ctx, &redis.XReadGroupArgs{
				Group:    c.groupName,
				Consumer: c.consumerName,
				Streams:  []string{c.streamName, ">"}, // '>' is the special ID for new messages
				Count:    readCount,
				Block:    readBlockTime,
				NoAck:    false, // We need to explicitly acknowledge
			}).Result()

			if err != nil {
				// redis.Nil indicates a timeout (no new messages within Block duration), which is normal
				if errors.Is(err, redis.Nil) {
					// log.Printf("[%s] No new messages received within block time", c.consumerName)
					continue
				}
				// Check if the error is due to context cancellation (shutdown)
				if errors.Is(err, context.Canceled) {
					log.Printf("[%s] XReadGroup cancelled by context", c.consumerName)
					return
				}
				log.Printf("[%s] Error reading from stream %s: %v. Retrying shortly...", c.consumerName, c.streamName, err)
				// Add a small delay before retrying after an unexpected error
				time.Sleep(2 * time.Second)
				continue
			}

			// Process received messages
			for _, stream := range streams { // Should only be one stream (c.streamName)
				for _, msg := range stream.Messages {
					c.processAndAck(msg)
				}
			}
		}
	}
}

// handlePendingMessagesLoop periodically checks for and claims old pending messages
func (c *Consumer) handlePendingMessagesLoop() {
	defer c.wg.Done()
	log.Printf("[%s] Starting pending message handling loop (check interval: %s)", c.consumerName, pendingCheckInterval)

	for {
		select {
		case <-c.ctx.Done():
			log.Printf("[%s] Stopping pending message handling loop due to context cancellation", c.consumerName)
			return
		case <-c.stopPendingCheck: // Allows immediate exit on Stop()
			log.Printf("[%s] Stopping pending message handling loop due to stop signal", c.consumerName)
			return
		case <-c.pendingCheckTicker.C:
			// log.Printf("[%s] Checking for pending messages older than %s", c.consumerName, minIdleTimeForClaim)

			// 1. Find pending messages for this group
			// We check pending messages for the *entire group*, not just this consumer,
			// as this consumer might be responsible for picking up work from crashed ones.
			pendingArgs := redis.XPendingExtArgs{
				Stream: c.streamName,
				Group:  c.groupName,
				Idle:   minIdleTimeForClaim,
				Start:  "-", // Start from the beginning of the pending list
				End:    "+", // Go to the end
				Count:  claimBatchSize,
				// Consumer: "", // Check for messages pending for *any* consumer in the group
			}

			pendingMsgs, err := c.redisClient.XPendingExt(c.ctx, &pendingArgs).Result()
			if err != nil {
				// NOGROUP error might happen if the group/stream was deleted externally.
				if strings.Contains(err.Error(), "NOGROUP") {
					log.Printf("[%s] Consumer group '%s' or stream '%s' not found during pending check. Stopping pending loop.", c.consumerName, c.groupName, c.streamName)
					// Consider maybe trying to recreate the group or stopping the consumer entirely
					return
				}
				log.Printf("[%s] Error checking pending messages: %v", c.consumerName, err)
				continue // Try again on the next tick
			}

			if len(pendingMsgs) == 0 {
				// log.Printf("[%s] No stale pending messages found", c.consumerName)
				continue
			}

			log.Printf("[%s] Found %d potentially stale message(s) pending for more than %s", c.consumerName, len(pendingMsgs), minIdleTimeForClaim)

			// 2. Claim the messages
			msgIDs := make([]string, len(pendingMsgs))
			for i, p := range pendingMsgs {
				msgIDs[i] = p.ID
			}

			claimArgs := redis.XClaimArgs{
				Stream:   c.streamName,
				Group:    c.groupName,
				Consumer: c.consumerName, // Claim for *this* consumer instance
				MinIdle:  minIdleTimeForClaim,
				Messages: msgIDs,
				// JUSTID: false, // We want the message content as well
			}

			claimedMsgs, err := c.redisClient.XClaim(c.ctx, &claimArgs).Result()
			if err != nil {
				log.Printf("[%s] Error claiming messages (%v): %v", c.consumerName, msgIDs, err)
				continue // Try again later
			}

			if len(claimedMsgs) == 0 {
				log.Printf("[%s] Claimed 0 messages (another consumer might have claimed them first)", c.consumerName)
				continue
			}

			log.Printf("[%s] Successfully claimed %d message(s)", c.consumerName, len(claimedMsgs))

			// 3. Process the claimed messages
			for _, msg := range claimedMsgs {
				log.Printf("[%s] Processing claimed message ID: %s", c.consumerName, msg.ID)
				c.processAndAck(msg) // Reuse the same processing logic
			}
		}
	}
}

// processAndAck handles the processing and acknowledgment of a single message
func (c *Consumer) processAndAck(msg redis.XMessage) {
	// Add a timeout to the processing step
	procCtx, procCancel := context.WithTimeout(c.ctx, processingTimeout)
	defer procCancel()

	// Extract the JSON data from the 'data' field
	jsonData, ok := msg.Values["data"].(string)
	if !ok {
		log.Printf("[%s] Error processing message %s: 'data' field is missing or not a string. Skipping.", c.consumerName, msg.ID)
		// Acknowledge even if malformed to prevent infinite loops? Or move to DLQ?
		// For simplicity, we ack here, but this decision depends on requirements.
		if err := c.acknowledge(msg.ID); err != nil {
			log.Printf("[%s] Failed to ACK malformed message %s: %v", c.consumerName, msg.ID, err)
		}
		return
	}

	var emailMsg EmailVerifyMessage
	err := json.Unmarshal([]byte(jsonData), &emailMsg)
	if err != nil {
		log.Printf("[%s] Error unmarshalling JSON for message %s: %v. Skipping.", c.consumerName, msg.ID, err)
		// Acknowledge malformed JSON? Depends on strategy. Acking for now.
		if err := c.acknowledge(msg.ID); err != nil {
			log.Printf("[%s] Failed to ACK malformed message %s: %v", c.consumerName, msg.ID, err)
		}
		return
	}

	// Call the user-provided processing function
	log.Printf("[%s] Processing message ID: %s, Email: %s", c.consumerName, msg.ID, emailMsg.Email)
	err = c.processor(procCtx, msg.ID, &emailMsg)

	if err != nil {
		log.Printf("[%s] Error processing message %s (Email: %s): %v. NOT acknowledging.", c.consumerName, msg.ID, emailMsg.Email, err)
		// Do NOT acknowledge the message if processing fails.
		// It will remain pending and hopefully be picked up later by this
		// or another consumer (via the handlePendingMessagesLoop).
		// Consider adding retry logic or moving to a Dead Letter Queue here.
		return
	}

	// Acknowledge successful processing
	err = c.acknowledge(msg.ID)
	if err != nil {
		log.Printf("[%s] Failed to ACK successfully processed message %s: %v", c.consumerName, msg.ID, err)
		// This is tricky. Processing succeeded, but ACK failed.
		// The message *might* be processed again later. Ensure processor is idempotent.
	} else {
		log.Printf("[%s] Successfully processed and acknowledged message ID: %s", c.consumerName, msg.ID)
	}
}

// acknowledge sends the XACK command for a given message ID
func (c *Consumer) acknowledge(messageID string) error {
	err := c.redisClient.XAck(c.ctx, c.streamName, c.groupName, messageID).Err()
	if err != nil && !errors.Is(err, context.Canceled) { // Ignore context canceled error during shutdown
		log.Printf("[%s] Error acknowledging message %s: %v", c.consumerName, messageID, err)
		return err
	}
	return nil
}

// --- Helper Functions ---

// getRedisClient initializes the Redis client (Standard or Sentinel)
func GetRedisClient(ctx context.Context) (redis.UniversalClient, error) {
	// --- !!! Configure Sentinel or Standard Connection Here !!! ---

	// Example: Standard Connection
	opts, err := redis.ParseURL(RedisURL)
	if err != nil {
		return nil, fmt.Errorf("invalid Redis URL: %w", err)
	}
	log.Printf("Connecting to Redis (Standard): %s", opts.Addr)
	client := redis.NewClient(opts)

	// Ping Redis to verify connection
	_, err = client.Ping(ctx).Result()
	if err != nil {
		client.Close() // Close client if ping fails
		panic(fmt.Errorf("failed to connect to Redis: %w", err))
	}

	log.Println("Successfully connected to Redis")
	return client, nil
}
