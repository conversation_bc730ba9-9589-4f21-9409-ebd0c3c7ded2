package utils

import (
	"log/slog"
	"os"

	"github.com/joho/godotenv"
)

// 加载.env文件，尝试多个可能的位置
func LoadEnvFile() {
	// 可能的.env文件位置
	possibleLocations := []string{
		".env",       // 项目根目录
		"../.env",    // 上一级目录
		"../../.env", // 上一级目录
	}

	for _, loc := range possibleLocations {
		if _, err := os.Stat(loc); err == nil {
			err = godotenv.Load(loc)
			if err == nil {
				slog.Info("成功加载.env文件", "path", loc)
				return
			}
			slog.Error("加载.env文件失败", "path", loc, "error", err)
		}
	}

	// 如果找不到.env文件，尝试从当前目录加载
	err := godotenv.Load()
	if err != nil {
		slog.Info("未找到.env文件，将使用环境变量或默认值")
	} else {
		slog.Info("成功加载当前目录的.env文件")
	}
}
