package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"gitlab.glm.ai/ai-search/z-ai-email-server/email"
	"gitlab.glm.ai/ai-search/z-ai-email-server/utils"
)

// --- Main Application ---

func main() {
	log.Println("Starting Redis Stream Consumer...")

	// --- Context for graceful shutdown ---
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// --- Setup signal handling ---
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)

	// --- Connect to Redis ---
	redisClient, err := utils.GetRedisClient(ctx)
	if err != nil {
		log.Fatalf("Failed to initialize Redis client: %v", err)
	}

	// --- Define the message processing logic ---
	// This is where your application's specific logic goes.
	// It should be IDEMPOTENT if possible, as messages might be redelivered
	// in case of failures before acknowledgment.
	messageHandler := func(procCtx context.Context, msgID string, msg *utils.EmailVerifyMessage) error {
		// Example processing: Just log the details
		log.Printf("--> Handler received message %s: Email=%s, User=%s, Token=%s @ %d",
			msgID, msg.Email, msg.Username, msg.Token, msg.Timestamp)

		workDone := make(chan struct{})

		var sendEmail func(msg *utils.EmailVerifyMessage)
		sendEmail = func(msg *utils.EmailVerifyMessage) {
			go func() {
				if msg.Event == "verify" {
					htmlBody, textBody, err := email.GetVerificationEmail(msg.Username, msg.VerifyLink, msg.Token, 60*24, msg.Language)
					if err != nil {
						log.Printf("生成邮件内容失败: %v", err)
						return
					}
					text := "请验证您的电子邮箱"
					if msg.Language != "zh" {
						text = "Verify your email"
					}
					// 发送验证邮件
					err = email.SendEmailWithTemplate(ctx, []string{msg.Email}, text, htmlBody, textBody)
					if err != nil {
						log.Printf("发送邮件失败: %v", err)
						return
					}

					log.Print("验证邮件已发送", "email", msg.Email, ", language:", msg.Language)

				} else if msg.Event == "welcome" {
					// 发送欢迎邮件 - 中文版本
					loginLink := msg.VerifyLink
					welcomeHTML, welcomeText, err := email.GetWelcomeEmail(msg.Username, loginLink, msg.Language)
					if err != nil {
						log.Printf("生成欢迎邮件内容失败: %v", err)
						return
					}
					text := "欢迎加入 Z Chat"
					if msg.Language != "zh" {
						text = "Welcome to Z Chat"
					}
					err = email.SendEmailWithTemplate(ctx, []string{msg.Email}, text, welcomeHTML, welcomeText)
					if err != nil {
						log.Printf("发送欢迎邮件失败: %v", err)
						return
					}

					log.Print("欢迎邮件已发送", "email", msg.Email, ", language:", msg.Language)

				} else if msg.Event == "password_reset" {
					// 发送密码重置邮件
					resetLink := msg.ResetLink
					resetHTML, resetText, err := email.GetPasswordResetEmail(msg.Username, resetLink, 30, msg.Language)
					if err != nil {
						log.Printf("生成密码重置邮件内容失败: %v", err)
						return
					}
					text := "重置您的密码"
					if msg.Language != "zh" {
						text = "Reset Your Password"
					}
					err = email.SendEmailWithTemplate(ctx, []string{msg.Email}, text, resetHTML, resetText)
					if err != nil {
						log.Printf("发送密码重置邮件失败: %v", err)
						return
					}

					log.Print("密码重置邮件已发送", "email", msg.Email, ", language:", msg.Language)

				} else if msg.Event == "password_reset_success" {
					// 发送密码重置成功邮件
					loginLink := msg.VerifyLink // 使用 VerifyLink 字段作为登录链接
					successHTML, successText, err := email.GetPasswordResetSuccessEmail(msg.Username, loginLink, msg.Language)
					if err != nil {
						log.Printf("生成密码重置成功邮件内容失败: %v", err)
						return
					}
					text := "密码重置成功"
					if msg.Language != "zh" {
						text = "Password Reset Successful"
					}
					err = email.SendEmailWithTemplate(ctx, []string{msg.Email}, text, successHTML, successText)
					if err != nil {
						log.Printf("发送密码重置成功邮件失败: %v", err)
						return
					}

					log.Print("密码重置成功邮件已发送", "email", msg.Email, ", language:", msg.Language)

				} else {
					log.Printf("--> Handler received unknown event: %s", msg.Event)
				}
				workDone <- struct{}{}
			}()

		}
		sendEmail(msg)
		// Simulate some work
		select {
		case <-workDone:
			log.Printf("--> Handler processing done for message %s", msgID)
			// Work done
		case <-procCtx.Done():
			log.Printf("--> Handler processing cancelled for message %s: %v", msgID, procCtx.Err())
			return procCtx.Err() // Return context error if processing timed out or was cancelled
		}

		return nil // Return nil on successful processing
	}

	// --- Create and start the consumer ---
	consumer, err := utils.NewConsumer(ctx, redisClient, utils.TargetTopic, utils.ConsumerGroupName, messageHandler)
	if err != nil {
		log.Fatalf("Failed to create consumer: %v", err)
	}

	consumer.Start()

	// --- Wait for shutdown signal ---
	<-sigChan
	log.Println("Received shutdown signal...")

	// --- Trigger graceful shutdown ---
	consumer.Stop()

	log.Println("Consumer application finished.")
}
