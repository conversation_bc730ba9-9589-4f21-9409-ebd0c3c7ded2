# 构建阶段
FROM golang:1.23.4-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -o email-server .

# 运行阶段
FROM alpine:latest

# 安装ca-certificates以支持HTTPS
RUN apk --no-cache add ca-certificates

# 创建非root用户
RUN adduser -D -g '' appuser

WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/email-server .

# 设置权限
RUN chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 应用启动命令
CMD ["./email-server"]
